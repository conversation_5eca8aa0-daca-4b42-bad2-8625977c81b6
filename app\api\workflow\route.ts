import { openai } from '@ai-sdk/openai'
import { anthropic } from '@ai-sdk/anthropic'
import { google } from '@ai-sdk/google'
import { generateObject } from 'ai'
import { planSchema, preDesignVariantsSchema } from '@/lib/schema'
import { toPlanningPrompt, toPreDesignPrompt } from '@/lib/prompt'
import { getModelClient } from '@/lib/models'

export async function POST(req: Request) {
  try {
    const { stage, userPrompt, plan, model, config } = await req.json()

    if (!model) {
      throw new Error('No model provided')
    }

    // Use the same model client logic as the main chat API
    const aiModel = getModelClient(model, config || {})

    if (stage === 'planning') {
      // Generate project plan
      const { object: generatedPlan } = await generateObject({
        model: aiModel,
        system: toPlanningPrompt(),
        prompt: userPrompt,
        schema: planSchema,
      })

      return new Response(JSON.stringify({
        success: true,
        plan: generatedPlan
      }), {
        headers: {
          'Content-Type': 'application/json',
        },
      })
    }

    if (stage === 'predesign' && plan) {
      // Generate PreDesign variants
      const { object: variants } = await generateObject({
        model: aiModel,
        system: toPreDesignPrompt(plan),
        prompt: `Create 3 design variants for this project: ${userPrompt}`,
        schema: preDesignVariantsSchema,
      })

      return new Response(JSON.stringify({
        success: true,
        variants
      }), {
        headers: {
          'Content-Type': 'application/json',
        },
      })
    }

    return new Response(JSON.stringify({
      success: false,
      error: 'Invalid stage or missing required data'
    }), {
      status: 400,
      headers: {
        'Content-Type': 'application/json',
      },
    })

  } catch (error) {
    console.error('Workflow API error:', error)
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}
