import { useState } from 'react'
import { PlanningWidget } from './planning-widget'
import { PreDesignViewer } from './predesign-viewer'
import { Preview } from './preview'
import { WorkflowState, WorkflowStage } from '@/lib/types'
import { PlanSchema, PreDesignSchema, PreDesignVariantsSchema } from '@/lib/schema'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, Clock, Eye, Code } from 'lucide-react'

interface WorkflowManagerProps {
  workflowState: WorkflowState
  onPlanAccept: () => void
  onPlanEdit: (plan: PlanSchema) => void
  onVariantSelect: (variant: PreDesignSchema) => void
  onPreDesignSave: () => void
  isLoading?: boolean
  loadingStage?: WorkflowStage
}

export function WorkflowManager({
  workflowState,
  onPlanAccept,
  onPlanEdit,
  onVariantSelect,
  onPreDesignSave,
  isLoading,
  loadingStage
}: WorkflowManagerProps) {
  
  const getStageIcon = (stage: WorkflowStage, currentStage: WorkflowStage) => {
    const isCompleted = getStageOrder(stage) < getStageOrder(currentStage)
    const isCurrent = stage === currentStage
    
    if (isCompleted) return <CheckCircle className="h-4 w-4 text-green-600" />
    if (isCurrent && isLoading) return <Clock className="h-4 w-4 text-blue-600 animate-spin" />
    if (isCurrent) return <Clock className="h-4 w-4 text-blue-600" />
    
    switch (stage) {
      case 'planning': return <Clock className="h-4 w-4 text-gray-400" />
      case 'predesign': return <Eye className="h-4 w-4 text-gray-400" />
      case 'coding': return <Code className="h-4 w-4 text-gray-400" />
      case 'complete': return <CheckCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStageOrder = (stage: WorkflowStage): number => {
    switch (stage) {
      case 'planning': return 1
      case 'predesign': return 2
      case 'coding': return 3
      case 'complete': return 4
      default: return 0
    }
  }

  const getStageStatus = (stage: WorkflowStage, currentStage: WorkflowStage) => {
    const isCompleted = getStageOrder(stage) < getStageOrder(currentStage)
    const isCurrent = stage === currentStage
    
    if (isCompleted) return 'completed'
    if (isCurrent) return 'current'
    return 'pending'
  }

  const getStageColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-gray-900 text-white border-gray-900'
      case 'current': return 'bg-gray-700 text-white border-gray-700'
      case 'pending': return 'bg-gray-100 text-gray-700 border-gray-300'
      default: return 'bg-gray-100 text-gray-700 border-gray-300'
    }
  }

  return (
    <div className="space-y-6">
      {/* Workflow Progress - Only show if not in complete stage */}
      {workflowState.stage !== 'complete' && (
        <Card className="border-2 border-gray-200 shadow-lg bg-white">
          <CardHeader className="bg-gray-50 border-b border-gray-200">
            <CardTitle className="text-gray-900 font-semibold">Project Workflow</CardTitle>
            <CardDescription className="text-gray-600">
              Follow the structured workflow to create your perfect application
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6 bg-white">
            <div className="flex items-center justify-between">
              {(['planning', 'predesign', 'coding', 'complete'] as WorkflowStage[]).map((stage, index) => (
                <div key={stage} className="flex items-center">
                  <div className="flex flex-col items-center">
                    <Badge
                      className={`px-3 py-1 border ${getStageColor(getStageStatus(stage, workflowState.stage))}`}
                    >
                      <span className="flex items-center gap-2 font-medium">
                        {getStageIcon(stage, workflowState.stage)}
                        {stage.charAt(0).toUpperCase() + stage.slice(1)}
                      </span>
                    </Badge>
                    {stage === loadingStage && isLoading && (
                      <div className="text-xs text-gray-600 mt-1 animate-pulse font-medium">
                        Processing...
                      </div>
                    )}
                  </div>
                  {index < 3 && (
                    <div className="w-12 h-px bg-gray-300 mx-3" />
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stage Content */}
      {workflowState.stage === 'planning' && workflowState.plan && (
        <PlanningWidget
          plan={workflowState.plan}
          onAccept={onPlanAccept}
          onEdit={onPlanEdit}
          isLoading={isLoading}
        />
      )}

      {workflowState.stage === 'predesign' && workflowState.preDesignVariants && (
        <PreDesignViewer
          variants={workflowState.preDesignVariants}
          onSelectVariant={onVariantSelect}
          onSavePreDesign={onPreDesignSave}
          selectedVariant={workflowState.selectedPreDesign}
          isLoading={isLoading}
        />
      )}

      {(workflowState.stage === 'coding' || workflowState.stage === 'complete') && workflowState.finalResult && (
        <Preview result={workflowState.finalResult} />
      )}
    </div>
  )
}
