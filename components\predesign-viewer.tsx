import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { PreDesignVariantsSchema, PreDesignSchema } from '@/lib/schema'
import { Eye, Code, Save, Palette, Sparkles, Zap } from 'lucide-react'

interface PreDesignViewerProps {
  variants: PreDesignVariantsSchema
  onSelectVariant: (variant: PreDesignSchema) => void
  onSavePreDesign: () => void
  selectedVariant?: PreDesignSchema
  isLoading?: boolean
}

export function PreDesignViewer({ 
  variants, 
  onSelectVariant, 
  onSavePreDesign, 
  selectedVariant,
  isLoading 
}: PreDesignViewerProps) {
  const [activeTab, setActiveTab] = useState('variant-0')

  const getVariantIcon = (index: number) => {
    switch (index) {
      case 0: return <Palette className="h-4 w-4" />
      case 1: return <Sparkles className="h-4 w-4" />
      case 2: return <Zap className="h-4 w-4" />
      default: return <Eye className="h-4 w-4" />
    }
  }

  const getVariantColor = (index: number) => {
    switch (index) {
      case 0: return 'border-gray-300 bg-gray-50'
      case 1: return 'border-gray-400 bg-gray-100'
      case 2: return 'border-gray-500 bg-gray-200'
      default: return 'border-gray-200 bg-gray-50'
    }
  }

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <Card className="border-2 border-gray-200 shadow-lg bg-white">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <CardTitle className="flex items-center gap-2 text-gray-900 font-semibold">
            <Eye className="h-5 w-5 text-gray-700" />
            PreDesign Mockups
          </CardTitle>
          <CardDescription className="text-gray-600">
            Choose your preferred design variant. Each mockup is fully interactive.
          </CardDescription>
          {variants.design_notes && (
            <div className="mt-2 p-3 bg-white rounded-md border border-gray-200">
              <p className="text-sm text-gray-700">{variants.design_notes}</p>
            </div>
          )}
        </CardHeader>
      </Card>

      {/* Variants Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          {variants.variants.map((variant, index) => (
            <TabsTrigger 
              key={index} 
              value={`variant-${index}`}
              className="flex items-center gap-2"
            >
              {getVariantIcon(index)}
              {variant.variant_name}
            </TabsTrigger>
          ))}
        </TabsList>

        {variants.variants.map((variant, index) => (
          <TabsContent key={index} value={`variant-${index}`} className="space-y-4">
            <Card className={`border-2 ${getVariantColor(index)}`}>
              <CardHeader className="bg-white">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2 text-gray-900">
                      {getVariantIcon(index)}
                      {variant.variant_name}
                    </CardTitle>
                    <CardDescription className="mt-2 text-gray-600">
                      {variant.design_description}
                    </CardDescription>
                  </div>
                  <Button
                    onClick={() => onSelectVariant(variant)}
                    variant={selectedVariant?.variant_name === variant.variant_name ? "default" : "outline"}
                    size="sm"
                    className={selectedVariant?.variant_name === variant.variant_name ? "bg-gray-900 hover:bg-gray-800 text-white" : "border-gray-300 text-gray-700 hover:bg-gray-50"}
                  >
                    {selectedVariant?.variant_name === variant.variant_name ? "Selected" : "Select"}
                  </Button>
                </div>

                {/* Key Features */}
                <div className="mt-4">
                  <h4 className="text-sm font-medium mb-2 text-gray-900">Key Features:</h4>
                  <div className="flex flex-wrap gap-2">
                    {variant.key_features.map((feature, featureIndex) => (
                      <Badge key={featureIndex} variant="outline" className="text-xs border-gray-300 text-gray-700">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                {/* Preview and Code Tabs */}
                <Tabs defaultValue="preview" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="preview" className="flex items-center gap-2">
                      <Eye className="h-4 w-4" />
                      Preview
                    </TabsTrigger>
                    <TabsTrigger value="code" className="flex items-center gap-2">
                      <Code className="h-4 w-4" />
                      Code
                    </TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="preview" className="mt-4">
                    <div className="border rounded-lg overflow-hidden bg-white">
                      <iframe
                        srcDoc={variant.html_content}
                        className="w-full h-96 border-0"
                        title={`${variant.variant_name} Preview`}
                        sandbox="allow-scripts allow-same-origin"
                      />
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="code" className="mt-4">
                    <div className="border rounded-lg overflow-hidden">
                      <pre className="bg-gray-900 text-gray-100 p-4 overflow-auto h-96 text-sm">
                        <code>{variant.html_content}</code>
                      </pre>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Save PreDesign Button */}
      {selectedVariant && (
        <Card className="border-2 border-gray-200 bg-gray-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-gray-900">Ready to Proceed?</h3>
                <p className="text-sm text-gray-700 mt-1">
                  Selected: <strong>{selectedVariant.variant_name}</strong>
                </p>
              </div>
              <Button
                onClick={onSavePreDesign}
                className="bg-gray-900 hover:bg-gray-800 text-white"
                disabled={isLoading}
              >
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? 'Processing...' : 'Save PreDesign & Proceed to Code'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
