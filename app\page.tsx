'use client'

import { ViewType } from '@/components/auth'
import { AuthDialog } from '@/components/auth-dialog'
import { Chat } from '@/components/chat'
import { ChatInput } from '@/components/chat-input'
import { ChatPicker } from '@/components/chat-picker'
import { ChatSettings } from '@/components/chat-settings'
import { NavBar } from '@/components/navbar'
import { Preview } from '@/components/preview'
import { WorkflowManager } from '@/components/workflow-manager'
import Logo from '@/components/logo'
import { useAuth } from '@/lib/auth'
import { Message, toAISDKMessages, toMessageImage } from '@/lib/messages'
import { LLMModelConfig } from '@/lib/models'
import modelsList from '@/lib/models.json'
import { FragmentSchema, fragmentSchema as schema, PlanSchema, PreDesignSchema } from '@/lib/schema'
import { supabase } from '@/lib/supabase'
import templates, { TemplateId } from '@/lib/templates'
import { ExecutionResult, WorkflowState, WorkflowStage } from '@/lib/types'
import { toCodingPrompt } from '@/lib/prompt'
import { DeepPartial } from 'ai'
import { experimental_useObject as useObject } from 'ai/react'
import { usePostHog } from 'posthog-js/react'
import { SetStateAction, useEffect, useState } from 'react'
import { useLocalStorage } from 'usehooks-ts'

export default function Home() {
  const [chatInput, setChatInput] = useLocalStorage('chat', '')
  const [files, setFiles] = useState<File[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState<'auto' | TemplateId>(
    'auto',
  )
  const [languageModel, setLanguageModel] = useLocalStorage<LLMModelConfig>(
    'languageModel',
    {
      model: 'claude-3-5-sonnet-latest',
    },
  )

  const posthog = usePostHog()

  // Workflow state
  const [workflowState, setWorkflowState] = useState<WorkflowState>({
    stage: 'planning'
  })
  const [isWorkflowLoading, setIsWorkflowLoading] = useState(false)
  const [workflowLoadingStage, setWorkflowLoadingStage] = useState<WorkflowStage>()

  // Legacy state for backward compatibility
  const [result, setResult] = useState<ExecutionResult>()
  const [messages, setMessages] = useState<Message[]>([])
  const [fragment, setFragment] = useState<DeepPartial<FragmentSchema>>()
  const [currentTab, setCurrentTab] = useState<'code' | 'fragment' | 'files'>('code')
  const [isPreviewLoading, setIsPreviewLoading] = useState(false)
  const [isAuthDialogOpen, setAuthDialog] = useState(false)
  const [authView, setAuthView] = useState<ViewType>('sign_in')
  const [isRateLimited, setIsRateLimited] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const { session, userTeam } = useAuth(setAuthDialog, setAuthView)

  const filteredModels = modelsList.models.filter((model) => {
    if (process.env.NEXT_PUBLIC_HIDE_LOCAL_MODELS) {
      return model.providerId !== 'ollama'
    }
    return true
  })

  const currentModel = filteredModels.find(
    (model) => model.id === languageModel.model,
  ) || filteredModels[0] // Fallback to first model if not found
  const currentTemplate =
    selectedTemplate === 'auto'
      ? templates
      : { [selectedTemplate]: templates[selectedTemplate] }
  const lastMessage = messages[messages.length - 1]

  // Workflow functions
  async function handleInitialPrompt(userPrompt: string) {
    if (!session) {
      return setAuthDialog(true)
    }

    if (!currentModel) {
      setErrorMessage('No valid model selected. Please select a model.')
      return
    }

    setIsWorkflowLoading(true)
    setWorkflowLoadingStage('planning')

    try {
      // Generate plan
      const planResponse = await fetch('/api/workflow', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          stage: 'planning',
          userPrompt,
          model: currentModel,
          config: languageModel,
        }),
      })

      const planData = await planResponse.json()

      if (planData.success) {
        setWorkflowState({
          stage: 'planning',
          plan: planData.plan
        })

        // Add user message
        addMessage({
          role: 'user',
          content: [{ type: 'text', text: userPrompt }],
        })

        // Add assistant message with plan
        addMessage({
          role: 'assistant',
          content: [{ type: 'text', text: `I've created a plan for your project: "${planData.plan.title}". Please review and approve it to proceed to the design stage.` }],
        })
      }
    } catch (error) {
      console.error('Planning error:', error)
      setErrorMessage('Failed to generate plan. Please try again.')
    } finally {
      setIsWorkflowLoading(false)
      setWorkflowLoadingStage(undefined)
    }
  }

  async function handlePlanAccept() {
    if (!workflowState.plan) return

    setIsWorkflowLoading(true)
    setWorkflowLoadingStage('predesign')

    try {
      // Generate PreDesign variants
      const preDesignResponse = await fetch('/api/workflow', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          stage: 'predesign',
          userPrompt: chatInput,
          plan: workflowState.plan,
          model: currentModel,
          config: languageModel,
        }),
      })

      const preDesignData = await preDesignResponse.json()

      if (preDesignData.success) {
        setWorkflowState(prev => ({
          ...prev,
          stage: 'predesign',
          preDesignVariants: preDesignData.variants
        }))

        addMessage({
          role: 'assistant',
          content: [{ type: 'text', text: 'Great! I\'ve created 3 design variants for your project. Please select your preferred design to proceed to the coding stage.' }],
        })
      }
    } catch (error) {
      console.error('PreDesign error:', error)
      setErrorMessage('Failed to generate design variants. Please try again.')
    } finally {
      setIsWorkflowLoading(false)
      setWorkflowLoadingStage(undefined)
    }
  }

  function handlePlanEdit(editedPlan: PlanSchema) {
    setWorkflowState(prev => ({
      ...prev,
      plan: editedPlan
    }))
  }

  function handleVariantSelect(variant: PreDesignSchema) {
    setWorkflowState(prev => ({
      ...prev,
      selectedPreDesign: variant
    }))
  }

  const { object, submit, isLoading, stop, error } = useObject({
    api: '/api/chat',
    schema,
    onError: (error) => {
      console.error('Error submitting request:', error)
      if (error.message.includes('limit')) {
        setIsRateLimited(true)
      }

      setErrorMessage(error.message)
    },
    onFinish: async ({ object: fragment, error }) => {
      if (!error) {
        console.log('fragment', fragment)
        setIsPreviewLoading(true)
        posthog.capture('fragment_generated', {
          template: fragment?.template,
        })

        const response = await fetch('/api/sandbox', {
          method: 'POST',
          body: JSON.stringify({
            fragment,
            userID: session?.user?.id,
            teamID: userTeam?.id,
            accessToken: session?.access_token,
          }),
        })

        const result = await response.json()
        console.log('result', result)
        posthog.capture('sandbox_created', { url: result.url })

        setResult(result)
        setWorkflowState(prev => ({
          ...prev,
          stage: 'complete',
          finalResult: result
        }))
        setCurrentPreview({ fragment, result })
        setMessage({ result })
        setCurrentTab('fragment')
        setIsPreviewLoading(false)
      }
    },
  })

  useEffect(() => {
    if (object) {
      setFragment(object)
      const content: Message['content'] = [
        { type: 'text', text: object.commentary || '' },
        { type: 'code', text: object.code || '' },
      ]

      if (!lastMessage || lastMessage.role !== 'assistant') {
        addMessage({
          role: 'assistant',
          content,
          object,
        })
      }

      if (lastMessage && lastMessage.role === 'assistant') {
        setMessage({
          content,
          object,
        })
      }
    }
  }, [object])

  useEffect(() => {
    if (error) stop()
  }, [error])

  function setMessage(message: Partial<Message>, index?: number) {
    setMessages((previousMessages) => {
      const updatedMessages = [...previousMessages]
      updatedMessages[index ?? previousMessages.length - 1] = {
        ...previousMessages[index ?? previousMessages.length - 1],
        ...message,
      }

      return updatedMessages
    })
  }

  async function handlePreDesignSave() {
    if (!workflowState.selectedPreDesign || !workflowState.plan) return

    setIsWorkflowLoading(true)
    setWorkflowLoadingStage('coding')

    try {
      // Create coding prompt with PreDesign reference
      const template = workflowState.plan.template as TemplateId
      const currentTemplate = { [template]: templates[template] }

      const content: Message['content'] = [{
        type: 'text',
        text: `Please implement the final code based on the approved plan and selected PreDesign variant: "${workflowState.selectedPreDesign.variant_name}". Use the PreDesign as a visual reference but recreate it properly using ${template} framework.`
      }]

      const updatedMessages = addMessage({
        role: 'user',
        content,
      })

      // Update workflow state to coding
      setWorkflowState(prev => ({
        ...prev,
        stage: 'coding'
      }))

      // Submit with coding prompt
      submit({
        userID: session?.user?.id,
        teamID: userTeam?.id,
        messages: toAISDKMessages(updatedMessages),
        template: currentTemplate,
        model: currentModel,
        config: languageModel,
        workflowContext: {
          stage: 'coding',
          plan: workflowState.plan,
          selectedPreDesign: workflowState.selectedPreDesign
        }
      })

      addMessage({
        role: 'assistant',
        content: [{ type: 'text', text: 'Perfect! I\'m now implementing your application based on the selected design. This may take a moment...' }],
      })

    } catch (error) {
      console.error('Coding error:', error)
      setErrorMessage('Failed to start coding phase. Please try again.')
    } finally {
      setIsWorkflowLoading(false)
      setWorkflowLoadingStage(undefined)
    }
  }

  async function handleSubmitAuth(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault()

    // Check if this is the initial prompt (no messages yet)
    if (messages.length === 0) {
      await handleInitialPrompt(chatInput)
      setChatInput('')
      setFiles([])
      return
    }

    if (!session) {
      return setAuthDialog(true)
    }

    if (isLoading) {
      stop()
    }

    const content: Message['content'] = [{ type: 'text', text: chatInput }]
    const images = await toMessageImage(files)

    if (images.length > 0) {
      images.forEach((image) => {
        content.push({ type: 'image', image })
      })
    }

    const updatedMessages = addMessage({
      role: 'user',
      content,
    })

    submit({
      userID: session?.user?.id,
      teamID: userTeam?.id,
      messages: toAISDKMessages(updatedMessages),
      template: currentTemplate,
      model: currentModel,
      config: languageModel,
    })

    setChatInput('')
    setFiles([])
    setCurrentTab('code')

    posthog.capture('chat_submit', {
      template: selectedTemplate,
      model: languageModel.model,
    })
  }

  function retry() {
    submit({
      userID: session?.user?.id,
      teamID: userTeam?.id,
      messages: toAISDKMessages(messages),
      template: currentTemplate,
      model: currentModel,
      config: languageModel,
    })
  }

  function addMessage(message: Message) {
    setMessages((previousMessages) => [...previousMessages, message])
    return [...messages, message]
  }

  function handleSaveInputChange(e: React.ChangeEvent<HTMLTextAreaElement>) {
    setChatInput(e.target.value)
  }

  function handleFileChange(change: SetStateAction<File[]>) {
    setFiles(change)
  }

  function logout() {
    supabase
      ? supabase.auth.signOut()
      : console.warn('Supabase is not initialized')
  }

  function handleLanguageModelChange(e: LLMModelConfig) {
    setLanguageModel({ ...languageModel, ...e })
  }

  function handleSocialClick(target: 'github' | 'x' | 'discord') {
    if (target === 'github') {
      window.open('https://github.com/e2b-dev/fragments', '_blank')
    } else if (target === 'x') {
      window.open('https://x.com/e2b_dev', '_blank')
    } else if (target === 'discord') {
      window.open('https://discord.gg/U7KEcGErtQ', '_blank')
    }

    posthog.capture(`${target}_click`)
  }

  function handleClearChat() {
    stop()
    setChatInput('')
    setFiles([])
    setMessages([])
    setFragment(undefined)
    setResult(undefined)
    setCurrentTab('code')
    setIsPreviewLoading(false)
    setWorkflowState({ stage: 'planning' })
    setIsWorkflowLoading(false)
    setWorkflowLoadingStage(undefined)
  }

  function setCurrentPreview(preview: {
    fragment: DeepPartial<FragmentSchema> | undefined
    result: ExecutionResult | undefined
  }) {
    setFragment(preview.fragment)
    setResult(preview.result)
  }

  function handleUndo() {
    setMessages((previousMessages) => [...previousMessages.slice(0, -2)])
    setCurrentPreview({ fragment: undefined, result: undefined })
  }

  // Check if we should show the centered welcome view
  const showWelcomeView = messages.length === 0

  return (
    <main className="flex min-h-screen max-h-screen welcome-transition">
      {supabase && (
        <AuthDialog
          open={isAuthDialogOpen}
          setOpen={setAuthDialog}
          view={authView}
          supabase={supabase}
        />
      )}

      {showWelcomeView ? (
        // Centered welcome view
        <div className="flex flex-col items-center justify-center w-full min-h-screen px-4 welcome-transition">
          <div className="w-full max-w-2xl">
            {/* Logo and Brand */}
            <div className="text-center mb-8">
              <div className="flex items-center justify-center gap-3 mb-6">
                <Logo width={32} height={32} className="text-foreground" />
                <h1 className="text-3xl font-bold text-foreground">Frmwrk</h1>
              </div>

              {/* Quote */}
              <h2 className="text-4xl font-bold mb-2 text-foreground">
                "Prompt and Witness"
              </h2>
              <p className="text-muted-foreground text-lg">
                Describe your vision, watch it come to life
              </p>
            </div>

            {/* Centered Chat Input */}
            <div className="w-full">
              <ChatInput
                retry={retry}
                isErrored={error !== undefined}
                errorMessage={errorMessage}
                isLoading={isLoading}
                isRateLimited={isRateLimited}
                stop={stop}
                input={chatInput}
                handleInputChange={handleSaveInputChange}
                handleSubmit={handleSubmitAuth}
                isMultiModal={currentModel?.multiModal || false}
                files={files}
                handleFileChange={handleFileChange}
              >
                <ChatPicker
                  templates={templates}
                  selectedTemplate={selectedTemplate}
                  onSelectedTemplateChange={setSelectedTemplate}
                  models={filteredModels}
                  languageModel={languageModel}
                  onLanguageModelChange={handleLanguageModelChange}
                />
                <ChatSettings
                  languageModel={languageModel}
                  onLanguageModelChange={handleLanguageModelChange}
                  apiKeyConfigurable={!process.env.NEXT_PUBLIC_NO_API_KEY_INPUT}
                  baseURLConfigurable={!process.env.NEXT_PUBLIC_NO_BASE_URL_INPUT}
                />
              </ChatInput>
            </div>
          </div>
        </div>
      ) : (
        // Normal layout after first message
        <div className="grid w-full md:grid-cols-2">
          <div
            className={`flex flex-col w-full max-h-full max-w-[800px] mx-auto px-4 overflow-auto ${(fragment || workflowState.stage !== 'planning') ? 'col-span-1' : 'col-span-2'}`}
          >
            <NavBar
              session={session}
              showLogin={() => setAuthDialog(true)}
              signOut={logout}
              onSocialClick={handleSocialClick}
              onClear={handleClearChat}
              canClear={messages.length > 0}
              canUndo={messages.length > 1 && !isLoading}
              onUndo={handleUndo}
            />

            {/* Show workflow manager for planning and predesign stages */}
            {(workflowState.stage === 'planning' || workflowState.stage === 'predesign') && (
              <div className="mb-4">
                <WorkflowManager
                  workflowState={workflowState}
                  onPlanAccept={handlePlanAccept}
                  onPlanEdit={handlePlanEdit}
                  onVariantSelect={handleVariantSelect}
                  onPreDesignSave={handlePreDesignSave}
                  isLoading={isWorkflowLoading}
                  loadingStage={workflowLoadingStage}
                />
              </div>
            )}

            <Chat
              messages={messages}
              isLoading={isLoading || isWorkflowLoading}
              setCurrentPreview={setCurrentPreview}
            />
            <ChatInput
              retry={retry}
              isErrored={error !== undefined}
              errorMessage={errorMessage}
              isLoading={isLoading || isWorkflowLoading}
              isRateLimited={isRateLimited}
              stop={stop}
              input={chatInput}
              handleInputChange={handleSaveInputChange}
              handleSubmit={handleSubmitAuth}
              isMultiModal={currentModel?.multiModal || false}
              files={files}
              handleFileChange={handleFileChange}
            >
              <ChatPicker
                templates={templates}
                selectedTemplate={selectedTemplate}
                onSelectedTemplateChange={setSelectedTemplate}
                models={filteredModels}
                languageModel={languageModel}
                onLanguageModelChange={handleLanguageModelChange}
              />
              <ChatSettings
                languageModel={languageModel}
                onLanguageModelChange={handleLanguageModelChange}
                apiKeyConfigurable={!process.env.NEXT_PUBLIC_NO_API_KEY_INPUT}
                baseURLConfigurable={!process.env.NEXT_PUBLIC_NO_BASE_URL_INPUT}
              />
            </ChatInput>
          </div>

          {/* Show workflow manager for coding/complete stages or regular preview */}
          {(workflowState.stage === 'coding' || workflowState.stage === 'complete') && workflowState.finalResult ? (
            <WorkflowManager
              workflowState={workflowState}
              onPlanAccept={handlePlanAccept}
              onPlanEdit={handlePlanEdit}
              onVariantSelect={handleVariantSelect}
              onPreDesignSave={handlePreDesignSave}
              isLoading={isWorkflowLoading}
              loadingStage={workflowLoadingStage}
              teamID={userTeam?.id}
              accessToken={session?.access_token}
              selectedTab={currentTab}
              onSelectedTabChange={setCurrentTab}
              isChatLoading={isLoading}
              isPreviewLoading={isPreviewLoading}
              fragment={fragment}
              onClose={() => setFragment(undefined)}
            />
          ) : fragment && (
            <Preview
              teamID={userTeam?.id}
              accessToken={session?.access_token}
              selectedTab={currentTab}
              onSelectedTabChange={setCurrentTab}
              isChatLoading={isLoading}
              isPreviewLoading={isPreviewLoading}
              fragment={fragment}
              result={result as ExecutionResult}
              onClose={() => setFragment(undefined)}
            />
          )}
        </div>
      )}
    </main>
  )
}